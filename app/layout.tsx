import type { Metadata } from 'next';
import { Poppins } from 'next/font/google';
import { getLocale, getMessages } from 'next-intl/server';
import { NextIntlClientProvider } from 'next-intl';
import { SUPPORTED_LOCALES } from '@/common/supportedLocales';
import { ModalProvider } from '@/contexts/ModalContext';
import '../styles/globals.css';
import '../styles/gradients.css';
import { AuthGuard } from '@/components/AuthGuard';
import { BgGradient } from '@/components/BgGradient';
import { ToastProvider } from '@/contexts/ToastContext';
import { SetupEnv } from '@/components/SetupEnv';

const poppins = Poppins({
    subsets: ['latin'],
    weight: ['400', '700'],
});

export const metadata: Metadata = {
    title: 'Empe One Click Deployment',
    description: 'One Click Deployment by Empe',
};

export default async function RootLayout({
    children,
}: Readonly<{
    children: React.ReactNode;
}>) {
    const locale = await getLocale().catch(() => {
        return SUPPORTED_LOCALES.en;
    });
    const messages = await getMessages();

    return (
        <html lang={locale}>
            <body className={`${poppins.className}`}>
                <BgGradient />
                <AuthGuard>
                    <NextIntlClientProvider messages={messages}>
                        <ToastProvider>
                            <SetupEnv>
                                <ModalProvider>{children}</ModalProvider>
                            </SetupEnv>
                        </ToastProvider>
                    </NextIntlClientProvider>
                </AuthGuard>
            </body>
        </html>
    );
}
