export enum ErrorTypes {
    UNKNOWN = 'UNKNOWN',
    GET_ENV_CONFIG = 'GET_ENV_CONFIG',
    GET_USER_DEPLOYMENTS = 'GET_USER_DEPLOYMENTS',
    GET_USER_DATA = 'GET_USER_DATA',
    DELETE_DEPLOYMENT = 'DELETE_DEPLOYMENT',
    GET_DEPLOYMENT_DETAILS = 'GET_DEPLOYMENT_DETAILS',
    USER_LOGIN = 'USER_LOGIN',
    USER_SIGNUP = 'USER_SIGNUP',
    CREATE_NEW_ISSUER = 'CREATE_NEW_ISSUER',
    CREATE_NEW_VERIFIER = 'CREATE_NEW_VERIFIER',
    UPDATE_DEPLOYMENT_SECRET = 'UPDATE_DEPLOYMENT_SECRET',
    UPGRADE_DEPLOYMENT_VERSION = 'UPGRADE_DEPLOYMENT_VERSION',
    GET_AVAILABLE_VERSIONS = 'GET_AVAILABLE_VERSIONS',
    ISSUER_ADMIN_ADD_SCHEMA = 'ISSUER_ADMIN_ADD_SCHEMA',
    SCHEMA_NOT_FOUND = 'SCHEMA_NOT_FOUND',
}
