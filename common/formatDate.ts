export function formatIsoToPlainDate(iso: string, options?: { timeZone?: string }): string {
    const date = new Date(iso);
    if (isNaN(date.getTime())) {
        throw new Error(`Nieprawidłowa data: ${iso}`);
    }

    return new Intl.DateTimeFormat('pl-PL', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        timeZone: options?.timeZone ?? 'Europe/Warsaw',
    }).format(date);
}

export function formatIsoToPlainTime(iso: string, options?: { timeZone?: string }): string {
    const date = new Date(iso);
    if (isNaN(date.getTime())) {
        throw new Error(`Nieprawidłowa data: ${iso}`);
    }

    return new Intl.DateTimeFormat('pl-PL', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false,
        timeZone: options?.timeZone ?? 'Europe/Warsaw',
    }).format(date);
}
