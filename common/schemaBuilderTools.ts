import { SchemaStatus } from '@/const/SchemaStatus';
import { IssuerSchema, IssuerSchemaBuilder } from '@/types/issuerSchema';
import { SchemaVersion, SchemaAttribute } from '@/types/schemaManager';

export const generatePropertiesRecursive = (
    attributes: SchemaAttribute[]
): { properties: Record<string, any>; required: string[] } => {
    const properties: Record<string, any> = {};
    const required: string[] = [];

    attributes.forEach(attr => {
        const property: any = {
            type: attr.dataType,
            title: attr.title,
            description: attr.description,
        };

        if (attr.format) {
            property.format = attr.format;
        }

        // If it's an object type with nested properties
        if (attr.dataType === 'object' && attr.properties && attr.properties.length > 0) {
            const nested = generatePropertiesRecursive(attr.properties);
            property.properties = nested.properties;
            if (nested.required.length > 0) {
                property.required = nested.required;
            }
        }

        properties[attr.name] = property;

        if (attr.required) {
            required.push(attr.name);
        }
    });

    return { properties, required };
};

export const generateJsonSchema = (schemaVersion: SchemaVersion): IssuerSchemaBuilder => {
    const { properties, required } = generatePropertiesRecursive(schemaVersion.attributes);

    return {
        $metadata: {
            version: schemaVersion.version,
            type: schemaVersion.type,
        },
        title: schemaVersion.name,
        description: schemaVersion.description,
        $schema: 'https://json-schema.org/draft/2020-12/schema',
        properties: {
            credentialSubject: {
                description: schemaVersion.description,
                title: schemaVersion.name,
                properties,
                required,
                type: 'object',
            },
            '@context': {
                type: ['string', 'array', 'object'],
            },
            id: {
                type: 'string',
            },
            issuanceDate: {
                format: 'date-time',
                type: 'string',
            },
            issuer: {
                type: ['string', 'object'],
                format: 'uri',
                properties: {
                    id: {
                        format: 'uri',
                        type: 'string',
                    },
                },
                required: ['id'],
            },
            type: {
                type: ['string', 'array'],
                items: {
                    type: 'string',
                },
            },
            credentialSchema: {
                properties: {
                    id: {
                        format: 'uri',
                        type: 'string',
                    },
                    type: {
                        type: 'string',
                    },
                },
                required: ['id', 'type'],
                type: 'object',
            },
        },
        required: ['credentialSubject', '@context', 'id', 'issuanceDate', 'issuer', 'type', 'credentialSchema'],
        type: 'object',
    };
};

export const sortedSchemaVersions = (schemas: IssuerSchema[]) => {
    return schemas.sort((a, b) => {
        // Definicja kolejności statusów
        const statusOrder = { PUBLISHED: 0, DRAFT: 1, DELETED: 2 };

        // Sortowanie po statusie
        const statusDiff =
            (statusOrder[a.status as keyof typeof statusOrder] ?? 999) -
            (statusOrder[b.status as keyof typeof statusOrder] ?? 999);
        if (statusDiff !== 0) return statusDiff;

        // Sortowanie po wersji (od najwyższej do najniższej)
        const versionDiff = b.version - a.version;
        if (versionDiff !== 0) return versionDiff;

        // Sortowanie po dacie modyfikacji (od najnowszej)
        const updatedAtDiff = new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime();
        if (updatedAtDiff !== 0) return updatedAtDiff;

        // Sortowanie po dacie utworzenia (od najnowszej)
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    });
};

export const getSchemaVersionListWithLatestOneDraft = (schemas: IssuerSchema[]): IssuerSchema[] => {
    // Rozdzielamy schematy na DRAFT i pozostałe (PUBLISHED, DELETED)
    const draftSchemas = schemas.filter(schema => schema.status === SchemaStatus.DRAFT);
    const nonDraftSchemas = schemas.filter(schema => schema.status !== SchemaStatus.DRAFT);

    // Grupujemy drafty po wersji
    const draftsByVersion = draftSchemas.reduce(
        (acc, schema) => {
            const version = schema.version;
            if (!acc[version]) {
                acc[version] = [];
            }
            acc[version].push(schema);
            return acc;
        },
        {} as Record<number, IssuerSchema[]>
    );

    // Dla każdej wersji draftów wybieramy ten z najpóźniejszą datą updatedAt
    const latestDrafts = Object.values(draftsByVersion).map(versionDrafts => {
        return versionDrafts.reduce((latest, current) => {
            const latestDate = new Date(latest.updatedAt).getTime();
            const currentDate = new Date(current.updatedAt).getTime();
            return currentDate > latestDate ? current : latest;
        });
    });

    // Łączymy wszystkie schematy niebędące draftami z najnowszymi draftami z każdej wersji
    return [...nonDraftSchemas, ...latestDrafts];
};

export const transformSchemaResponseToSchema = (schema: IssuerSchema): IssuerSchemaBuilder => {
    return {
        $metadata: {
            version:
                typeof schema.schemaBody.$metadata.version === 'string'
                    ? parseInt(schema.schemaBody.$metadata.version) || 1
                    : schema.schemaBody.$metadata.version,
            type: schema.schemaBody.$metadata.type,
        },
        title: schema.schemaBody.title,
        description: schema.description,
        $schema: 'https://json-schema.org/draft/2020-12/schema',
        properties: {
            credentialSubject: {
                description: schema.description,
                title: schema.schemaBody.title,
                properties: schema.schemaBody.properties.credentialSubject.properties,
                required: schema.schemaBody.properties.credentialSubject.required,
                type: schema.schemaBody.properties.credentialSubject.type,
            },
            '@context': schema.schemaBody.properties['@context'],
            id: schema.schemaBody.properties.id,
            issuanceDate: schema.schemaBody.properties.issuanceDate,
            issuer: schema.schemaBody.properties.issuer,
            type: schema.schemaBody.properties.type,
            credentialSchema: schema.schemaBody.properties.credentialSchema,
        },
        required: schema.schemaBody.required,
        type: schema.schemaBody.type,
    };
};
