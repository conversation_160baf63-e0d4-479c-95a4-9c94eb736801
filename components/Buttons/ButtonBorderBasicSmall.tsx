import clsx from 'clsx';
import { LoaderSpinnerSmall } from '../Loaders';

interface Props extends React.ButtonHTMLAttributes<HTMLButtonElement> {
    children?: React.ReactNode;
    isLoading?: boolean;
}

export const ButtonBorderBasicSmall = ({ children, isLoading, className, ...props }: Props) => {
    return (
        <button
            className={clsx(
                className,
                'border border-white whitespace-nowrap text-sm px-6 py-1 rounded-xl w-full text-center'
            )}
            {...props}
        >
            {isLoading ? <LoaderSpinnerSmall /> : (children ?? 'Click')}
        </button>
    );
};
