import clsx from 'clsx';
import { LoaderSpinnerSmall } from '../Loaders';

interface Props extends React.ButtonHTMLAttributes<HTMLButtonElement> {
    children?: React.ReactNode;
    isLoading?: boolean;
    active?: boolean;
}

export const ButtonBorderIcon = ({ children, isLoading, active = true, ...props }: Props) => {
    return (
        <button
            className={clsx(
                ' text-sm disabled:opacity-50 whitespace-nowrap disabled:cursor-not-allowed w-full text-center px-3 py-1  rounded-xl',
                active ? 'border-gradient bg-transparent' : 'border-gradient-solid bg-main-600/5'
            )}
            {...props}
        >
            {isLoading ? <LoaderSpinnerSmall /> : (children ?? 'Click')}
        </button>
    );
};
