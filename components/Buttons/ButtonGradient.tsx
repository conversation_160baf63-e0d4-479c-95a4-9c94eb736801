import { LoaderSpinnerSmall } from '../Loaders';

interface Props extends React.ButtonHTMLAttributes<HTMLButtonElement> {
    children?: React.ReactNode;
    isLoading?: boolean;
}

export const ButtonGradient = ({ children, isLoading, ...props }: Props) => {
    return (
        <button
            className={
                'item-gradient disabled:opacity-50 text-sm disabled:cursor-not-allowed w-full text-center whitespace-nowrap text-white py-3 px-6 rounded-md'
            }
            {...props}
        >
            {isLoading ? <LoaderSpinnerSmall /> : (children ?? 'Click')}
        </button>
    );
};
