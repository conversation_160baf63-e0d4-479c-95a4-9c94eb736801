import Image from 'next/image';

interface Props extends React.ButtonHTMLAttributes<HTMLButtonElement> {
    imageSrc: string;
    imageAlt: string;
}

export const ButtonOnlyImageSquare = ({ imageAlt, imageSrc, ...props }: Props) => {
    return (
        <button {...props} className="flex justify-center items-center border border-main-600 rounded-lg h-10 min-w-10">
            <Image src={imageSrc} alt={imageAlt} className="cursor-pointer" width={16} height={16} />
        </button>
    );
};
