import { useController, UseControllerProps } from 'react-hook-form';
import clsx from 'clsx';
import { InputHTMLAttributes } from 'react';
import Image from 'next/image';

interface Props extends Omit<InputHTMLAttributes<HTMLInputElement>, 'defaultValue'>, UseControllerProps {
    name: string;
    defaultValue?: boolean;
    mainLabel: string;
    label?: string;
    disabled?: boolean;
    href?: string;
}

export const Checkbox = ({
    name,
    control,
    defaultValue = false,
    rules,
    label,
    mainLabel,
    disabled = false,
    className,
    href,
}: Props) => {
    const {
        field: { value, onChange },
    } = useController({ name, control, defaultValue, rules });

    const handleToggle = () => {
        if (!disabled) {
            const newValue = !value;
            onChange(newValue);
        }
    };

    const elementLabel = label && <label className="block text-main-800 text-sm mb-1">{label}</label>;

    const elementInput = (
        <div
            className={clsx('flex items-center gap-2 cursor-pointer', disabled && 'opacity-50 cursor-not-allowed')}
            onClick={handleToggle}
        >
            <Image
                src={value ? '/assets/checkbox_check.png' : '/assets/checkbox.png'}
                alt={value ? 'Checked' : 'Unchecked'}
                width={32}
                height={32}
                className={clsx('w-8 h-8 opacity-90', disabled && 'cursor-not-allowed')}
            />
            <span className={clsx('text-main-100 underline', className)}>
                <a href={href} target="_blank">
                    {mainLabel}
                </a>
            </span>
        </div>
    );

    return (
        <div className="flex flex-col w-full">
            {elementInput}
            {elementLabel}
        </div>
    );
};

interface PropsWithoutRHF extends Omit<InputHTMLAttributes<HTMLInputElement>, 'defaultValue'> {
    name: string;
    mainLabel: string;
    label?: string;
    disabled?: boolean;
    href?: string;
    checked?: boolean;
}

export const CheckboxWithoutRHF = ({
    name,
    checked = false,
    label,
    mainLabel,
    disabled = false,
    className,
    onChange,
}: PropsWithoutRHF) => {
    const handleToggle = () => {
        if (!disabled && onChange) {
            const newValue = !checked;
            // Tworzymy event object podobny do natywnego checkbox input
            const syntheticEvent = {
                target: {
                    checked: newValue,
                    name: name,
                    value: newValue.toString(),
                },
            } as React.ChangeEvent<HTMLInputElement>;
            onChange(syntheticEvent);
        }
    };

    const elementLabel = label && <label className="block text-main-800 text-sm mb-1">{label}</label>;

    const elementInput = (
        <div
            className={clsx('flex items-center gap-2 cursor-pointer', disabled && 'opacity-50 cursor-not-allowed')}
            onClick={handleToggle}
        >
            <Image
                src={checked ? '/assets/checkbox_check.png' : '/assets/checkbox.png'}
                alt={checked ? 'Checked' : 'Unchecked'}
                width={32}
                height={32}
                className={clsx('w-8 h-8 opacity-90', disabled && 'cursor-not-allowed')}
            />
            <span className={clsx('text-main-600 h-full', className)}>{mainLabel}</span>
        </div>
    );

    return (
        <div className="flex flex-col w-full">
            {elementInput}
            {elementLabel}
        </div>
    );
};
