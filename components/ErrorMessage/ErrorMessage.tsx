import { useModal } from '@/hooks/useModal';
import { useTranslations } from 'next-intl';
import { ButtonBorder } from '../Buttons';
import { ErrorClassFields } from '@/types/errorClass';

export const ErrorMessage = ({ type, code, message }: ErrorClassFields) => {
    const t = useTranslations('error_modal_status');
    const { hideModal } = useModal();

    const messageElement = message ? <p className="text-white text-center">{message}</p> : null;

    return (
        <div className="flex flex-col gap-4 justify-center w-56 md:w-80">
            <h2 className="text-2xl font-bold text-white">{t('title')}</h2>
            <p className="text-white text-center">{t(type.toLowerCase())}</p>
            <p className="text-white text-center">{code}</p>
            {messageElement}
            <ButtonBorder onClick={hideModal}>{t('close_button')}</ButtonBorder>
        </div>
    );
};
