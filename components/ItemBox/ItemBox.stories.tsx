import type { Meta, StoryObj } from '@storybook/react';
import { ItemBox } from './ItemBox';

const meta: Meta<typeof ItemBox> = {
    title: 'Components/ItemBox',
    component: ItemBox,
    tags: ['autodocs'],
    parameters: {
        layout: 'centered',
    },
};

export default meta;
type Story = StoryObj<typeof ItemBox>;

export const Default: Story = {
    args: {
        children: 'Przykładowa zawartość',
    },
};

export const WithLongContent: Story = {
    args: {
        children: (
            <div>
                <h3>Tytuł</h3>
                <p>Dłuższa przykładowa zawartość z różnymi elementami</p>
            </div>
        ),
    },
};

export const WithCustomContent: Story = {
    args: {
        children: (
            <div className="p-4">
                <button className="bg-blue-500 text-white px-4 py-2 rounded">Przycisk w boxie</button>
            </div>
        ),
    },
};
