import { LinkProps } from 'next/link';
import Link from 'next/link';

interface Props extends LinkProps, Omit<React.AnchorHTMLAttributes<HTMLAnchorElement>, 'href'> {
    children?: React.ReactNode;
}

export const LinkBorderBasic = ({ children, href, ...props }: Props) => {
    return (
        <Link
            href={href}
            target="_blank"
            className="border-main-600 border text-sm flex justify-center px-4 items-center disabled:opacity-50 whitespace-nowrap disabled:cursor-not-allowed text-main-600 overflow-hidden text-center rounded-md"
            {...props}
        >
            {children}
        </Link>
    );
};
