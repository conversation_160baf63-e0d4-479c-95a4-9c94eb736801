import Link, { LinkProps } from 'next/link';

interface Props extends LinkProps, Omit<React.AnchorHTMLAttributes<HTMLAnchorElement>, 'href'> {
    children?: React.ReactNode;
}

export const LinkBorderSmall = ({ children, href, ...props }: Props) => {
    return (
        <Link
            href={href}
            className="border-gradient text-sm disabled:opacity-50 whitespace-nowrap disabled:cursor-not-allowed w-full text-center text-main-600 px-6 py-1 overflow-hidden rounded-xl"
            {...props}
        >
            {children ?? 'Click'}
        </Link>
    );
};
