import type { Meta, StoryObj } from '@storybook/react';
import { ModalProvider } from '@/contexts/ModalContext';
import * as React from 'react';
import { ModalWrapper } from './ModalWrapper';
import { messages } from '@/.storybook/i18n-mock';
import { NextIntlClientProvider } from 'next-intl';

const meta: Meta<typeof ModalWrapper> = {
    title: 'Modals/ModalWrapper',
    component: ModalWrapper,
    parameters: {
        layout: 'centered',
    },
    tags: ['autodocs'],
    decorators: [
        Story => (
            <NextIntlClientProvider messages={messages} locale="en">
                <ModalProvider>
                    <ModalWrapper>
                        <Story />
                    </ModalWrapper>
                </ModalProvider>
            </NextIntlClientProvider>
        ),
    ],
} satisfies Meta<typeof ModalWrapper>;

export default meta;
type Story = StoryObj<typeof ModalWrapper>;

export const Default: Story = {
    args: {
        children: (
            <div className="w-[400px] h-[300px] flex items-center justify-center">
                <h2 className="text-xl font-bold">Example Modal Content</h2>
            </div>
        ),
    },
};

export const LargeContent: Story = {
    args: {
        children: (
            <div className="w-[600px] h-[400px] flex flex-col gap-4 items-center justify-center">
                <h2 className="text-2xl font-bold">Large Modal</h2>
                <p className="text-gray-600">This is an example of a modal with larger content area.</p>
                <button className="px-4 py-2 bg-blue-500 text-white rounded">Example Button</button>
            </div>
        ),
    },
};

export const SmallContent: Story = {
    args: {
        children: (
            <div className="w-[200px] h-[150px] flex items-center justify-center">
                <p className="text-sm">Small Modal Content</p>
            </div>
        ),
    },
};
