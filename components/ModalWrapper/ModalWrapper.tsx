'use client';
import Image from 'next/image';
import { ReactNode } from 'react';
import { ItemBox } from '../ItemBox';
import { useModal } from '@/hooks/useModal';
import clsx from 'clsx';

interface Props {
    children: ReactNode | null;
    fullSize?: boolean;
}

const CloseButton = () => {
    const { hideModal } = useModal();
    return (
        <button onClick={hideModal} className="absolute top-5 right-5 text-gray-500 hover:text-gray-700">
            <Image src="/assets/CloseIcon.svg" alt="close" width={16} height={16} />
        </button>
    );
};

export const ModalWrapper = ({ children, fullSize = false }: Props) => {
    if (!children) return null;
    return (
        <div className="fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center z-10">
            <div className="w-fit h-fit mx-4">
                <ItemBox>
                    <div className="p-8 w-full h-full relative text-center">
                        <CloseButton />
                        <div className={clsx('flex flex-col gap-4 justify-center', !fullSize && 'w-64 md:w-96')}>
                            <div className="flex flex-col items-center justify-center h-full gap-10 pt-6">
                                {children}
                            </div>
                        </div>
                    </div>
                </ItemBox>
            </div>
        </div>
    );
};
