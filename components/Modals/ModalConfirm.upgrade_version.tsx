import { useTranslations } from 'next-intl';
import { ButtonBorder } from '../Buttons';

interface Props {
    onConfirm: () => void;
}

const LOCALE_KEY = 'confirm_upgrade_version';

export const ModalConfirmUpgradeVersion = ({ onConfirm }: Props) => {
    const t = useTranslations('modals');

    return (
        <>
            <h2 className="text-2xl font-bold text-white">{t(`${LOCALE_KEY}_title`)}</h2>
            <p className="text-white text-center">{t(`${LOCALE_KEY}_description`)}</p>
            <ButtonBorder onClick={onConfirm}>{t(`${LOCALE_KEY}_button`)}</ButtonBorder>
        </>
    );
};
