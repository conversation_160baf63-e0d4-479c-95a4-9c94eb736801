import { useTranslations } from 'next-intl';
import { ButtonGradient } from '../Buttons';
import { useErrorHandling } from '@/hooks/useErrorHandling';
import { ErrorCreateNewIssuerSchema } from '@/errors/ErrorCreateNewIssuerSchema';
import { useToast } from '@/hooks/useToast';
import { handlePostIssuerSchema } from '@/api/issuerSchema';
import { SuccessToast } from '../Toasts';
import { useModal } from '@/hooks/useModal';
import { useGetAndSetSchemasOfIssuer } from '@/hooks/useGetAndSetSchemasOfIssuer';
import { useState } from 'react';

interface Props {
    fullHost: string;
    authKey: string;
    callback?: () => void;
    payload: Record<string, unknown>;
}

const LOCALE_KEY = 'modals.logic_add_schema';

export const ModalLogicAddSchema = ({ fullHost, callback, authKey, payload }: Props) => {
    const t = useTranslations();
    const [loading, setLoading] = useState(false);
    const { withErrorHandling } = useErrorHandling();
    const { refetch } = useGetAndSetSchemasOfIssuer({
        fullHost,
        authorization: authKey,
    });
    const { showToast } = useToast();
    const { hideModal } = useModal();

    const onSubmit = () =>
        withErrorHandling(async () => {
            try {
                setLoading(true);
                await handlePostIssuerSchema({
                    fullHost,
                    authorization: authKey,
                    payload,
                });
                console.log('🚀 ~ onSubmit ~ fullHost:', fullHost);
                await refetch();
                if (callback) {
                    callback();
                }
                showToast(<SuccessToast title={t('toast.success')} message={t('toast.add_new_schema')} />);
                hideModal();
            } catch (error) {
                console.log('🚀 ~ onSubmit ~ error:', error);
                throw new ErrorCreateNewIssuerSchema(error);
            } finally {
                setLoading(false);
            }
        });

    return (
        <div className="flex text-left flex-col gap-4 p-6">
            <h2 className="text-2xl text-center font-bold text-white">{t(`${LOCALE_KEY}_title`)}</h2>
            <p className="text-white text-center">{t(`${LOCALE_KEY}_description`)}</p>
            <ButtonGradient onClick={onSubmit} isLoading={loading} disabled={loading}>
                {t(`${LOCALE_KEY}_button`)}
            </ButtonGradient>
        </div>
    );
};
