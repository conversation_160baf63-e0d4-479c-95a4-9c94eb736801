'use client';

import { useTranslations } from 'next-intl';
import { ButtonOnlyImageSquare } from '../Buttons';
import { useState, useEffect } from 'react';
import Editor from '@monaco-editor/react';
import { useModal } from '@/hooks/useModal';

interface Props {
    initialJson: string;
    onSave: (jsonString: string) => { success: boolean; error?: string };
}

export const ModalLogicJsonEditor = ({ initialJson, onSave }: Props) => {
    const t = useTranslations('schema_builder');
    const { hideModal } = useModal();

    const [editedJsonString, setEditedJsonString] = useState(initialJson);
    const [jsonValidationError, setJsonValidationError] = useState<string | null>(null);

    useEffect(() => {
        setEditedJsonString(initialJson);
    }, [initialJson]);

    const handleJsonChange = (value: string | undefined) => {
        const jsonValue = value || '';
        setEditedJsonString(jsonValue);
        setJsonValidationError(null);

        // Real-time validation
        if (jsonValue.trim()) {
            try {
                const parsed = JSON.parse(jsonValue);

                // Additional validation for schema structure
                if (!parsed.$metadata || !parsed.title || !parsed.properties?.credentialSubject) {
                    setJsonValidationError(
                        'Invalid schema structure. Missing required fields ($metadata, title, properties.credentialSubject)'
                    );
                }
            } catch {
                setJsonValidationError('Invalid JSON format');
            }
        }
    };

    const handleFormatJson = () => {
        try {
            const parsed = JSON.parse(editedJsonString);
            const formatted = JSON.stringify(parsed, null, 2);
            setEditedJsonString(formatted);
            setJsonValidationError(null);
        } catch {
            setJsonValidationError('Cannot format invalid JSON');
        }
    };

    const handleSave = async () => {
        if (jsonValidationError) return;

        try {
            const result = onSave(editedJsonString);
            if (result.success) {
                hideModal();
            } else {
                setJsonValidationError(result.error || 'Unknown error occurred');
            }
        } catch {
            setJsonValidationError('Error saving schema');
        }
    };

    return (
        <div className="flex flex-col gap-6 w-full max-w-6xl mx-auto">
            <div className="text-center">
                <h2 className="text-2xl font-bold text-white mb-2">{t('edit_json')}</h2>
                <p className="text-main-600">{t('edit_json_description')}</p>
            </div>

            <div className="space-y-4">
                <div className="flex gap-3 justify-end">
                    <ButtonOnlyImageSquare
                        imageSrc="/icons/save.svg"
                        imageAlt="Save"
                        onClick={handleSave}
                        disabled={!!jsonValidationError || !editedJsonString.trim()}
                    />
                    <ButtonOnlyImageSquare
                        imageSrc="/icons/format.svg"
                        imageAlt="remove attribute"
                        disabled={!editedJsonString.trim()}
                        onClick={handleFormatJson}
                    />
                </div>
                <div className="border border-main-800/30 rounded overflow-hidden">
                    <Editor
                        height="500px"
                        defaultLanguage="json"
                        value={editedJsonString}
                        onChange={handleJsonChange}
                        theme="vs-dark"
                        options={{
                            minimap: { enabled: false },
                            scrollBeyondLastLine: false,
                            fontSize: 13,
                            lineNumbers: 'on',
                            roundedSelection: false,
                            scrollbar: {
                                vertical: 'visible',
                                horizontal: 'visible',
                            },
                            automaticLayout: true,
                            formatOnPaste: true,
                            formatOnType: true,
                            wordWrap: 'on',
                            bracketPairColorization: {
                                enabled: true,
                            },
                            colorDecorators: true,
                            folding: true,
                            foldingHighlight: true,
                            showFoldingControls: 'always',
                            matchBrackets: 'always',
                            renderWhitespace: 'selection',
                            tabSize: 2,
                            insertSpaces: true,
                        }}
                    />
                </div>

                {jsonValidationError && (
                    <div className="text-red-400 text-sm bg-red-900/20 border border-red-800/30 rounded p-3">
                        {jsonValidationError}
                    </div>
                )}
            </div>
        </div>
    );
};
