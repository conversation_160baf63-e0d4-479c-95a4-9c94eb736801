import { useTranslations } from 'next-intl';
import { renderFormFields } from '@/common/renderFormFields';
import { DeploymentType } from '@/types/deployments';
import { useShowLogs } from '@/hooks/useShowLogs';
import { TimeRangeFormInputsTypes } from '@/validation/logsSchemaValidation';
import { useLogsStore } from '@/store/logsStore';
import { LogEntry } from '../Logs';
import { useCopyAndDownload } from '@/hooks/useCopyAndDownload';
import { LoaderSpinnerSmall } from '../Loaders';
import { ButtonBorderBasic } from '../Buttons/ButtonBorderBasic';
import Image from 'next/image';

interface Props {
    deploymentId: string;
    type: DeploymentType;
}

const LOCALE_KEY = 'show_logs';

export const ModalLogicShowLogs = ({ deploymentId, type }: Props) => {
    const t = useTranslations('modals');
    const { logs } = useLogsStore();

    const {
        trigger,
        formFields,
        register,
        handleSubmit,
        errors,
        isValid,
        onSubmit,
        isSubmitting,
        control,
        onReset,
        isLoading,
    } = useShowLogs({ type, deploymentId });

    const { handleCopyToClipboard, handleDownloadFile } = useCopyAndDownload();

    const fieldsToRender = renderFormFields({
        formFields,
        register,
        errors,
        t,
        formInputsTypes: TimeRangeFormInputsTypes,
        trigger,
        control,
    });

    const renderLogs = () => {
        if (isLoading)
            return (
                <div className="flex flex-row justify-center items-center py-16">
                    <LoaderSpinnerSmall />
                </div>
            );
        if (logs.length === 0) return <p className="text-center text-gray-400">{t(`${LOCALE_KEY}_no_logs`)}</p>;
        return (
            <>
                {logs.map((log, index) => (
                    <div key={index} className="w-full break-all">
                        <LogEntry log={log} />
                    </div>
                ))}
            </>
        );
    };

    return (
        <div className="flex flex-col py-box-50 px-box-100 ">
            <form onSubmit={handleSubmit(onSubmit)} className="flex flex-row justify-between items-end gap-4">
                <div className="flex flex-row gap-4 [&_input]:w-[220px]">{fieldsToRender}</div>
                <div className="flex flex-1 flex-row max-w-[600px]  justify-between items-end pb-1 gap-4">
                    <ButtonBorderBasic disabled={!isValid} isLoading={isSubmitting}>
                        {t(`${LOCALE_KEY}_button`)}
                    </ButtonBorderBasic>
                    <ButtonBorderBasic disabled={!isValid} type="button" onClick={onReset}>
                        {t(`${LOCALE_KEY}_button_clear`)}
                    </ButtonBorderBasic>
                    <div className="flex justify-center items-center border border-main-600 rounded-lg h-12 min-w-12">
                        <Image
                            src="/assets/icon-Copy.svg"
                            className="cursor-pointer"
                            onClick={() => handleCopyToClipboard(logs)}
                            alt="close"
                            width={20}
                            height={20}
                        />
                    </div>
                    <div className="flex justify-center items-center border border-main-600 rounded-lg h-12 min-w-12">
                        <Image
                            src="/assets/icon-DownloadSimple.svg"
                            className="cursor-pointer"
                            onClick={() => handleDownloadFile(logs, 'logs')}
                            alt="close"
                            width={20}
                            height={20}
                        />
                    </div>
                </div>
            </form>
            <div className="overflow-auto mt-4 gap-2 flex flex-col without-scrollbar w-full text-left px-4 py-4 bg-transparent rounded-lg">
                {renderLogs()}
            </div>
        </div>
    );
};
