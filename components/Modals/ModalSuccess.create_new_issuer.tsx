import { useTranslations } from 'next-intl';
import { useModal } from '@/hooks/useModal';
import { ButtonBorder } from '../Buttons';
import { CopyAuthKey } from '../CopyAuthKey';

interface Props {
    authKey: string;
}

const LOCALE_KEY = 'success_create_new_issuer';

export const ModalSuccessCreateNewIssuer = ({ authKey }: Props) => {
    const t = useTranslations('modals');
    const { hideModal } = useModal();

    return (
        <>
            <h2 className="text-2xl font-bold text-white">{t(`${LOCALE_KEY}_title`)}</h2>
            <CopyAuthKey authKey={authKey} />
            <p className="text-white text-center">{t(`${LOCALE_KEY}_info_key`)}</p>
            <ButtonBorder onClick={hideModal}>{t(`${LOCALE_KEY}_button`)}</ButtonBorder>
        </>
    );
};
