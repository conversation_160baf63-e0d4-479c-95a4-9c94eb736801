import { useTranslations } from 'next-intl';
import { useModal } from '@/hooks/useModal';
import { ButtonBorder } from '../Buttons';

const LOCALE_KEY = 'success_upgrade_version';

export const ModalSuccessUpgradeVersion = () => {
    const t = useTranslations('modals');
    const { hideModal } = useModal();

    return (
        <>
            <h2 className="text-2xl font-bold text-white">{t(`${LOCALE_KEY}_title`)}</h2>
            <p className="text-white text-center">{t(`${LOCALE_KEY}_description`)}</p>
            <ButtonBorder onClick={hideModal}>{t(`${LOCALE_KEY}_button`)}</ButtonBorder>
        </>
    );
};
