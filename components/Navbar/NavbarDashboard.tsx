'use client';
import Image from 'next/image';
import { RouterPaths } from '@/common/routerPaths';

export const NavbarDashboard = () => {
    return (
        <>
            <div className="h-16 bg-main-700 flex w-full relative flex-row justify-between items-center gap-4 px-4 md:px-10">
                <a href={RouterPaths.DASHBOARD}>
                    <Image
                        src="/assets/NavLogo.svg"
                        alt="logo"
                        className="w-64 md:w-[200px] cursor-pointer"
                        width={200}
                        height={100}
                    />
                </a>

                <div className="flex flex-row-reverse md:flex-row w-full md:w-auto items-center gap-24 md:gap-4">
                    <Image src="/assets/BurgerIcon.svg" alt="logo" className="cursor-pointer" width={24} height={24} />
                    {/* <Image src={avatar.icon} alt="avatar" width={40} height={40} onClick={handleChangeAvatarName} />
                    <ButtonGradientSmall onClick={handlePortalClick}>Manage Subscription</ButtonGradientSmall>
                    <LinkBorderSmall href={resetPasswordUrl}>{t('request_password_reset')}</LinkBorderSmall> */}
                </div>
            </div>
            <div className="item-gradient w-full h-1" />
        </>
    );
};
