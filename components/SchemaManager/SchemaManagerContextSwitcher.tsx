import { SchemaBuilderStepEnum, useSchemaBuilderContext } from '@/contexts/SchemaBuilderContext';
import { SchemaBuilder } from './SchemaBuilder';
import { SchemaList } from './SchemaList';

export const SchemaBuilderContextSwitcher = () => {
    const { step } = useSchemaBuilderContext();

    if (step === SchemaBuilderStepEnum.SCHEMA_LIST) return <SchemaList />;
    if (step === SchemaBuilderStepEnum.SCHEMA_BUILDER) return <SchemaBuilder />;
};
