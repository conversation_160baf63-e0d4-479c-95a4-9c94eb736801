'use client';

import { useState, useEffect, useRef } from 'react';
import { Editor, type Monaco } from '@monaco-editor/react';
import OneDarkPro from '@/theme/marco.custom.json';
import { SchemaVersion } from '@/types/schemaManager';

interface Props {
    json: string;
    onSave: (parsed: SchemaVersion) => void;
}

export const JsonEditor = ({ json, onSave }: Props) => {
    const [jsonValidationError, setJsonValidationError] = useState<string | null>(null);
    const [internalJson, setInternalJson] = useState<string>('');
    const isInternalUpdate = useRef(false);

    // Format JSON if it's not already formatted
    const formatJsonIfNeeded = (jsonString: string) => {
        try {
            const parsed = JSON.parse(jsonString);
            return JSON.stringify(parsed, null, 2);
        } catch {
            return jsonString; // Return original if parsing fails
        }
    };

    // Initialize and sync with external JSON changes
    useEffect(() => {
        if (!isInternalUpdate.current) {
            const formatted = formatJsonIfNeeded(json);
            setInternalJson(formatted);
        }
        isInternalUpdate.current = false;
    }, [json]);

    const handleJsonChange = (value: string | undefined) => {
        const jsonValue = value || '';
        setInternalJson(jsonValue);
        setJsonValidationError(null);
        isInternalUpdate.current = true;

        // Real-time validation and save
        if (jsonValue.trim()) {
            try {
                const parsed = JSON.parse(jsonValue);

                // Additional validation for schema structure
                if (!parsed.$metadata || !parsed.title || !parsed.properties?.credentialSubject) {
                    setJsonValidationError(
                        'Invalid schema structure. Missing required fields ($metadata, title, properties.credentialSubject)'
                    );
                    return;
                }

                // Only call onSave if JSON is valid and complete
                onSave(parsed);
            } catch {
                setJsonValidationError('Invalid JSON format');
            }
        }
    };

    // TODO: Add a button to format the JSON
    // const handleFormatJson = () => {
    //     try {
    //         const parsed = JSON.parse(internalJson);
    //         const formatted = JSON.stringify(parsed, null, 2);
    //         setInternalJson(formatted);
    //         setJsonValidationError(null);
    //     } catch {
    //         setJsonValidationError('Cannot format invalid JSON');
    //     }
    // };

    const handleEditorDidMount = (monaco: Monaco) => {
        // Konwersja tokenColors na rules Monaco (obsługa tablic scope)
        const rules = OneDarkPro.tokenColors
            // eslint-disable-next-line
            .flatMap((tokenColor: any) => {
                const scopes = Array.isArray(tokenColor.scope) ? tokenColor.scope : [tokenColor.scope].filter(Boolean); // Obsługa array lub string, filtr pustych
                return scopes.map((scope: string) => ({
                    token: scope || '', // Monaco używa 'token' jako scope
                    foreground: tokenColor.settings?.foreground?.replace('#', '') || undefined,
                    background: tokenColor.settings?.background?.replace('#', '') || undefined,
                    fontStyle: tokenColor.settings?.fontStyle || undefined, // np. 'italic bold underline'
                }));
            })
            .filter(rule => rule.token); // Filtruj tylko te z token (scope)

        // Pełny temat Monaco
        const monacoTheme = {
            base: 'vs-dark' as const, // Lub 'vs' dla jasnego, ale One Dark jest dark
            inherit: true, // Dziedzicz z base
            rules, // Przetworzone reguły syntax
            colors: OneDarkPro.colors,
        };

        // Zdefiniuj temat
        monaco.editor.defineTheme('OneDarkPro', monacoTheme);
    };

    return (
        <div className="flex flex-col h-full gap-2">
            <Editor
                defaultLanguage="json"
                value={internalJson}
                onChange={handleJsonChange}
                theme="OneDarkPro"
                beforeMount={handleEditorDidMount}
                className="bg-transparent h-full"
                options={{
                    minimap: { enabled: false },
                    scrollBeyondLastLine: false,
                    fontSize: 13,
                    lineNumbers: 'on',
                    roundedSelection: false,
                    scrollbar: {
                        vertical: 'hidden',
                        horizontal: 'hidden',
                    },
                    automaticLayout: true,
                    formatOnPaste: true,
                    formatOnType: true,
                    wordWrap: 'on',
                    bracketPairColorization: {
                        enabled: true,
                    },
                    colorDecorators: true,
                    folding: true,
                    foldingHighlight: true,
                    showFoldingControls: 'always',
                    matchBrackets: 'always',
                    renderWhitespace: 'selection',
                    tabSize: 2,
                    insertSpaces: true,
                    // Usuwanie obramowania i tła
                    glyphMargin: false,
                    overviewRulerBorder: false,
                    hideCursorInOverviewRuler: true,
                }}
            />

            {jsonValidationError && (
                <div className="text-red-400 text-sm bg-red-900/20 border border-red-800/30 rounded p-3">
                    {jsonValidationError}
                </div>
            )}
        </div>
    );
};
