import { SchemaVersion } from '@/types/schemaManager';
import Image from 'next/image';
import { TextWithSmallLabel } from './Labels';
import { ButtonGradientSmall } from '@/components/Buttons';
import { ButtonBorderIcon } from '@/components/Buttons/ButtonBorderIcon';
import { ColumnsKeys } from '../SchemaBuilder';
import { DraftSavingIndicator } from './SaveLoad';

interface Props {
    schemaVersion: SchemaVersion;
    activeColumns: Array<string>;
    isDraftSaving: boolean;
    lastAutoSaved?: Date | null;
    onBackButtonClick: () => void;
    onColumnClick: (columnKey: string) => void;
    onPublish: () => void;
}

export const SchemaBuilderNavbar = ({
    schemaVersion,
    activeColumns,
    isDraftSaving,
    lastAutoSaved,
    onBackButtonClick,
    onColumnClick,
    onPublish,
}: Props) => {
    const actions = [
        {
            label: 'Attributes',
            key: ColumnsKeys.ATTRIBUTES,
            image: '/icons/attributes_bar.svg',
        },
        {
            label: 'JSON',
            key: ColumnsKeys.JSON_PREVIEW,
            image: '/icons/json_bar.svg',
        },
        {
            label: 'Ai',
            key: ColumnsKeys.AI,
            image: '/icons/ai_bar.svg',
        },
        {
            label: 'History',
            key: ColumnsKeys.HISTORY,
            image: '/icons/history_bar.svg',
        },
    ];

    const renderBack = () => {
        return (
            <div className="flex flex-row gap-2 text-main-100 items-center cursor-pointer" onClick={onBackButtonClick}>
                <Image src="/assets/back_arrow_blue.svg" alt="back arrow" width={8} height={8} />
                <span className="text-sm">Back to Schema List</span>
            </div>
        );
    };

    const renderVersionDetails = () => {
        return (
            <div className="flex flex-row justify-between items-center gap-4 border-main-1100 bg-main-600/5 rounded-full border px-4">
                <TextWithSmallLabel label="name:" text={schemaVersion.name} />
                <div className="w-0.5 h-full border-l border-main-1100" />
                <TextWithSmallLabel label="type:" text={schemaVersion.type} />
                <div className="w-0.5 h-full border-l border-main-1100" />
                <TextWithSmallLabel label="v." text={schemaVersion.version.toString()} />
            </div>
        );
    };

    const renderActions = () => {
        return (
            <div className="flex flex-row justify-between gap-4 relative">
                <div className="absolute -left-36 top-1/2 -translate-y-1/2">
                    <DraftSavingIndicator isSaving={isDraftSaving} lastSaved={lastAutoSaved} />
                </div>
                {actions.map(action => (
                    <ButtonBorderIcon
                        type="button"
                        active={activeColumns.includes(action.key)}
                        key={action.key}
                        onClick={() => onColumnClick?.(action.key)}
                    >
                        <Image src={action.image} alt={`${action.label}`} width={18} height={18} />
                    </ButtonBorderIcon>
                ))}
                <ButtonGradientSmall onClick={onPublish}>Publish</ButtonGradientSmall>
            </div>
        );
    };

    return (
        <div className="flex flex-row justify-between py-2">
            {renderBack()}
            {renderVersionDetails()}
            {renderActions()}
        </div>
    );
};
