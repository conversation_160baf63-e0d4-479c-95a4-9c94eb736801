'use client';
import { ReactNode, useState } from 'react';
import clsx from 'clsx';
import { useSubscriptionLink } from '@/hooks/useSubscriptionLink';

interface Props {
    children: ReactNode;
    content: string;
    disabled?: boolean;
    position?: 'top' | 'bottom' | 'left' | 'right';
}

export const Tooltip = ({ children, content, disabled = false, position = 'right' }: Props) => {
    const [isVisible, setIsVisible] = useState(false);
    const { handlePortalClick } = useSubscriptionLink();

    if (disabled) {
        return <>{children}</>;
    }

    const positionClasses = {
        top: 'bottom-full left-0 mb-2',
        bottom: 'top-full left-0 mt-2',
        left: 'right-full top-1/2 transform -translate-y-1/2 mr-0',
        right: 'left-full top-1/2 transform -translate-y-1/2 ml-0',
    };

    const arrowClasses = {
        top: 'top-full left-4 border-l-transparent border-r-transparent border-b-transparent border-t-main-1300',
        bottom: 'bottom-full left-4 border-l-transparent border-r-transparent border-t-transparent border-b-main-1300',
        left: 'left-full top-1/2 transform -translate-y-1/2 border-t-transparent border-b-transparent border-r-transparent border-l-main-1300',
        right: 'right-full top-1/2 transform -translate-y-1/2 border-t-transparent border-b-transparent border-l-transparent border-r-main-1300',
    };

    return (
        <div
            className="relative inline-block"
            onMouseEnter={() => setIsVisible(true)}
            onMouseLeave={() => setIsVisible(false)}
        >
            {children}
            {isVisible && (
                <div
                    className={clsx(
                        'absolute z-50 px-3 py-2 flex flex-col gap-2 text-center text-sm text-main-600 bg-main-1300 rounded-lg shadow-lg w-64 ',
                        positionClasses[position]
                    )}
                >
                    {content}
                    <button
                        onClick={handlePortalClick}
                        className="border hover:opacity-80 rounded-md px-4 text-sm py-1"
                    >
                        Manage Subscription
                    </button>
                    <div className={clsx('absolute w-0 h-0 border-4', arrowClasses[position])} />
                </div>
            )}
        </div>
    );
};
