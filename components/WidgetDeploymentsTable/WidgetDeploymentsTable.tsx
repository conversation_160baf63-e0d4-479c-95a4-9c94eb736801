import { type Deployment } from '@/types/deployments';
import { TableItem } from './TableItem';
import { useTranslations } from 'next-intl';
import { EmptyTableInfo } from './EmptyTableInfo';
import { LoaderSpinnerSmall } from '../Loaders';
import Image from 'next/image';
import { RouterPaths } from '@/common/routerPaths';
import Link from 'next/link';

interface Props {
    deployments: Deployment[];
    isLoading: boolean;
}

const EmptyWidgetDeploymentsTable = () => {
    const t = useTranslations();

    return (
        <div className="w-full h-full flex gap-10 flex-col text-main-600 p-4 pb-8">
            <h1 className="flex flex-1 text-lg">{t('deployments_table.widget_title')}</h1>
            <div className="flex-[2] flex justify-center items-center">
                <EmptyTableInfo />
            </div>
            <div className="flex flex-row gap-6 self-center">
                <Link
                    href={RouterPaths.NEW_ISSUER}
                    className="flex items-center border whitespace-nowrap text-main-600 gap-4 p-4 px-8 hover:bg-white/10 rounded-lg transition-all duration-300 ease-in-out"
                >
                    <Image src={'/assets/icon-Certificate.svg'} alt="" width={24} height={24} />
                    <span>New Issuer</span>
                </Link>
                <Link
                    href={RouterPaths.NEW_VERIFIER}
                    className="flex items-center border whitespace-nowrap text-main-600 gap-4 p-4 px-8 hover:bg-white/10 rounded-lg transition-all duration-300 ease-in-out"
                >
                    <Image src={'/assets/icon-ShieldStar.svg'} alt="" width={24} height={24} />
                    <span>New Verifier</span>
                </Link>
            </div>
        </div>
    );
};

export const WidgetDeploymentsTable = ({ deployments, isLoading }: Props) => {
    const t = useTranslations();

    if (isLoading) {
        return (
            <div className="w-full h-full flex py-10 justify-center items-center">
                <LoaderSpinnerSmall />
            </div>
        );
    }

    if (deployments.length === 0) {
        return <EmptyWidgetDeploymentsTable />;
    }

    return (
        <div className="w-full text-main-600 h-full overflow-hidden">
            <h1 className="p-4 text-lg">{t('deployments_table.widget_title')}</h1>
            <div className="h-full overflow-y-auto without-scrollbar scroll-smooth">
                <table className="w-full">
                    <thead>
                        <tr className="text-left text-xs [&>th]:px-4 [&>th]:py-1">
                            <th>{t('deployments_table.name')}</th>
                            <th>{t('deployments_table.type')}</th>
                            <th>{t('deployments_table.status')}</th>
                            <th />
                        </tr>
                    </thead>
                    <tbody>
                        {deployments.map((item, index) => {
                            return <TableItem deployment={item} key={`deploy-${index}-${item.id}`} />;
                        })}
                    </tbody>
                </table>
            </div>
        </div>
    );
};
