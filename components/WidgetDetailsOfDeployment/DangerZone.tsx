import { useTranslations } from 'next-intl';
import { ButtonBorderBasic } from '../Buttons/ButtonBorderBasic';

interface Props {
    updateVersionAvailable: boolean;
    handleDelete: () => void;
    handleUpgradeSecret: () => void;
    handleUpgradeVersion: () => void;
}

export const DangerZone = ({
    handleUpgradeSecret,
    handleDelete,
    handleUpgradeVersion,
    updateVersionAvailable,
}: Props) => {
    const t = useTranslations('details_of_deployment');

    const rednerUpdateVersion = () => {
        if (!updateVersionAvailable) return null;
        return (
            <div className="flex items-center justify-between px-6 py-4">
                <div className="flex-1">
                    <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100">
                        {t('danger_zone.upgrade_version.title')}
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                        {t('danger_zone.upgrade_version.description')}
                    </p>
                </div>
                <div className="ml-4">
                    <ButtonBorderBasic
                        onClick={handleUpgradeVersion}
                        className="border-red-600 text-red-600 hover:bg-red-600 hover:text-white"
                    >
                        {t('danger_zone.upgrade_version.button')}
                    </ButtonBorderBasic>
                </div>
            </div>
        );
    };

    return (
        <div className="px-box-100 py-box-50">
            <div className="border border-red-600/50 rounded-lg overflow-hidden">
                {/* Header */}
                <div className=" border-b border-red-600/50 px-6 py-4">
                    <h2 className="text-lg font-semibold text-main-600">{t('danger_zone.title')}</h2>
                    <p className="text-sm text-main-600/50  mt-1">{t('danger_zone.subtitle')}</p>
                </div>

                {/* Actions */}
                <div className="">
                    {rednerUpdateVersion()}

                    {/* Upgrade Secret Action */}
                    <div className="flex items-center justify-between px-6 py-4">
                        <div className="flex-1">
                            <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100">
                                {t('danger_zone.upgrade_secret.title')}
                            </h3>
                            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                                {t('danger_zone.upgrade_secret.description')}
                            </p>
                        </div>
                        <div className="ml-4">
                            <ButtonBorderBasic
                                onClick={handleUpgradeSecret}
                                className="border-red-600 text-red-600 hover:bg-red-600 hover:text-white"
                            >
                                {t('danger_zone.upgrade_secret.button')}
                            </ButtonBorderBasic>
                        </div>
                    </div>

                    {/* Delete Action */}
                    <div className="flex items-center justify-between px-6 py-4 border-b border-red-200 dark:border-red-800/30 last:border-b-0">
                        <div className="flex-1">
                            <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100">
                                {t('danger_zone.delete_deployment.title')}
                            </h3>
                            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                                {t('danger_zone.delete_deployment.description')}
                            </p>
                        </div>
                        <div className="ml-4">
                            <ButtonBorderBasic
                                onClick={handleDelete}
                                className="border-red-600 text-red-600 hover:bg-red-600 hover:text-white"
                            >
                                {t('danger_zone.delete_deployment.button')}
                            </ButtonBorderBasic>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};
