import clsx from 'clsx';
import Link from 'next/link';

interface Props {
    activeTab: string;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    setActiveTab: any;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    tabs: any[];
}

export const Tabs = ({ activeTab, setActiveTab, tabs }: Props) => {
    return (
        <div className="flex flex-row gap-4">
            {tabs.map((item, index) => {
                if (item.hidden) return null;

                // Generate a unique key based on item properties or index
                const uniqueKey = item.key || `tab-${item.label}-${index}`;

                if (item.key) {
                    return (
                        <button
                            onClick={() => setActiveTab(item.key)}
                            key={uniqueKey}
                            className={clsx(
                                'bg-main-1300/50 text-main-600 rounded-t-lg py-1 px-4 text-sm',
                                activeTab === item.key
                                    ? 'border-transparent'
                                    : 'border-b border-main-600/20 text-main-600/40'
                            )}
                        >
                            {item.label}
                        </button>
                    );
                }
                if (item.href) {
                    return (
                        <Link
                            key={uniqueKey}
                            href={item.href}
                            target={item.target || '_blank'}
                            className={clsx(
                                'bg-main-1300/50 text-main-600 rounded-t-lg py-1 px-4 text-sm',
                                activeTab === item.key
                                    ? 'border-transparent'
                                    : 'border-b border-main-600/20 text-main-600/40'
                            )}
                        >
                            {item.label}
                        </Link>
                    );
                }
                if (item.onClick) {
                    return (
                        <button
                            onClick={item.onClick}
                            key={uniqueKey}
                            className={clsx(
                                'bg-main-1300/50 text-main-600 rounded-t-lg py-1 px-4 text-sm',
                                activeTab === item.key
                                    ? 'border-transparent'
                                    : 'border-b border-main-600/20 text-main-600/40'
                            )}
                        >
                            {item.label}
                        </button>
                    );
                }

                return null;
            })}
        </div>
    );
};
