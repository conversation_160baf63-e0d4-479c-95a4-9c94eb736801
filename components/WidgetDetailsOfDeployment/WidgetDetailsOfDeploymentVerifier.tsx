'use client';

import { Deployment, DeploymentStatus, DeploymentType } from '@/types/deployments';
import { ModalLogicShowLogs } from '@/components/Modals';
import { useLogsStore } from '@/store/logsStore';
import { useEffect, useState } from 'react';
import { ItemBoxBottomRadius } from '../ItemBox';
import { useGetAvailableVersions } from '@/hooks/useGetAvailableVersions';
import { Tabs } from './Tabs';
import { DangerZone } from './DangerZone';
import { TableItemStatus } from '../WidgetDeploymentsTable/TableItemStatus';

enum VERIFIER_TABS_KEYS {
    DETAILS = 'details',
    LOGS = 'logs',
    INSTRUCTION = 'instruction',
    UPGRADE_VERSION = 'upgrade_version',
    ACTIONS = 'actions',
}

interface Props {
    type: DeploymentType;
    status: DeploymentStatus;
    deployment: Deployment;
    handleDelete: () => void;
    handleUpgradeSecret: () => void;
    handleUpgradeVersion: () => void;
}

const API_DOCS_PREFIX = '/api-docs';

const formatKey = (key: string): string => {
    const withSpaces = key.replace(/([a-z0-9])([A-Z])/g, '$1 $2').replace(/[_-]+/g, ' ');
    return withSpaces
        .split(' ')
        .map(w => w.charAt(0).toUpperCase() + w.slice(1))
        .join(' ');
};

const getInstructionLink = (type: DeploymentType): string => {
    return `https://docs.empe.io/develop/${type}`;
};

export const WidgetDetailsOfDeploymentVerifier = ({
    type,
    deployment,
    handleDelete,
    handleUpgradeSecret,
    handleUpgradeVersion,
}: Props) => {
    const [activeTab, setActiveTab] = useState<VERIFIER_TABS_KEYS>(VERIFIER_TABS_KEYS.DETAILS);
    const { setLogs } = useLogsStore();
    const { versions } = useGetAvailableVersions({
        type: type,
        deploymentCurrentVersion: deployment.version,
    });

    useEffect(() => {
        setLogs([]);
    }, [setLogs]);

    const VERIFIER_TABS = [
        {
            label: 'Details of issuer',
            key: VERIFIER_TABS_KEYS.DETAILS,
        },
        {
            label: 'Logs',
            key: VERIFIER_TABS_KEYS.LOGS,
        },
        {
            label: 'Instruction',
            href: getInstructionLink(type),
        },
        // {
        //     label: 'Upgrade Version',
        //     hidden: versions.length <= 0,
        //     onClick: handleUpgradeVersion,
        // },
        {
            label: 'Actions',
            key: VERIFIER_TABS_KEYS.ACTIONS,
        },
    ];

    const renderDetails = () => {
        return (
            <div className="flex flex-col h-full overflow-auto without-scrollbar">
                <div className="flex w-full flex-col justify-center border-b border-gray-700 px-10 py-2 gap-2">
                    <div className="text-xs text-main-600/50">{formatKey('id')}</div>
                    <span className="text-md">{deployment.id}</span>
                </div>
                <div className="flex w-full flex-col justify-center border-b border-gray-700 px-10 py-2 gap-2">
                    <div className="text-xs text-main-600/50">{formatKey('fullHost')}</div>
                    <span className="text-md">{deployment.fullHost}</span>
                </div>
                <div className="flex w-full flex-col justify-center border-b border-gray-700 px-10 py-2 gap-2">
                    <div className="text-xs text-main-600/50">{formatKey('swaggerApi')}</div>
                    <a
                        className="text-md text-blue-500 hover:text-blue-600"
                        target="_blank"
                        href={deployment.fullHost + API_DOCS_PREFIX}
                    >
                        {deployment.fullHost + API_DOCS_PREFIX}
                    </a>
                </div>
                <div className="flex w-full flex-col justify-center border-b border-gray-700 px-10 py-2 gap-2">
                    <div className="text-xs text-main-600/50">{formatKey('status')}</div>
                    <TableItemStatus status={deployment.status} />
                </div>
                <div className="flex w-full flex-col justify-center border-b border-gray-700 px-10 py-2 gap-2">
                    <div className="text-xs text-main-600/50">{formatKey('networkName')}</div>
                    <span className="text-md">{deployment.networkName}</span>
                </div>
                <div className="flex w-full flex-col justify-center border-b border-gray-700 px-10 py-2 gap-2">
                    <div className="text-xs text-main-600/50">{formatKey('version')}</div>
                    <span className="text-md">{deployment.version}</span>
                </div>
                {deployment.verifierName && (
                    <>
                        <div className="flex w-full flex-col justify-center  px-10 py-2 gap-2">
                            <div className="text-xs text-main-600/50">{formatKey('verifierDomainName')}</div>
                            <span className="text-md">{deployment.verifierName}</span>
                        </div>
                        <div className="flex w-full flex-col justify-center  px-10 py-2 gap-2">
                            <div className="text-xs text-main-600/50">{formatKey('verifierName')}</div>
                            <span className="text-md">{deployment.verifierReadableName}</span>
                        </div>
                    </>
                )}
            </div>
        );
    };

    const renderLogs = () => {
        return <ModalLogicShowLogs deploymentId={deployment.id} type={type} />;
    };

    const renderActions = () => {
        return (
            <DangerZone
                updateVersionAvailable={versions.length > 0}
                handleDelete={handleDelete}
                handleUpgradeSecret={handleUpgradeSecret}
                handleUpgradeVersion={handleUpgradeVersion}
            />
        );
    };

    return (
        <div>
            <Tabs activeTab={activeTab} setActiveTab={setActiveTab} tabs={VERIFIER_TABS} />
            <ItemBoxBottomRadius>
                <div className="py-box-50">
                    {activeTab === VERIFIER_TABS_KEYS.DETAILS && renderDetails()}
                    {activeTab === VERIFIER_TABS_KEYS.LOGS && renderLogs()}
                    {activeTab === VERIFIER_TABS_KEYS.ACTIONS && renderActions()}
                </div>
            </ItemBoxBottomRadius>
        </div>
    );
};
