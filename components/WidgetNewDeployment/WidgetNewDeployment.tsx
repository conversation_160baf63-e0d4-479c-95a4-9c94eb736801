import { RouterPaths } from '@/common/routerPaths';
import { LinkBorder } from '../Links';
import { useTranslations } from 'next-intl';

export const WidgetNewDeployment = () => {
    const t = useTranslations('new_deployment');

    return (
        <div className="flex flex-col p-4 gap-3 w-full">
            <h1 className="text-lg">{t('title')}</h1>
            <p className="text-center">{t('description')}</p>
            <div className="flex gap-4 justify-center">
                <LinkBorder href={RouterPaths.NEW_ISSUER}>{t('new_issuer')}</LinkBorder>
                <LinkBorder href={RouterPaths.NEW_VERIFIER}>{t('new_verifier')}</LinkBorder>
            </div>
        </div>
    );
};
