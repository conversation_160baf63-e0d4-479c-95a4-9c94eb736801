import { useQuery, useQueryClient } from '@tanstack/react-query';
import { handleGetIssuerSchemas, handleGetIssuerSchemasDraft } from '@/api/issuerSchema';

const queryKey = (fullHost?: string, authorization?: string) => ['issuer-schemas', fullHost ?? '', authorization ?? ''];

const fetchSchemas = async (fullHost: string, authorization: string) => {
    const [schemas, drafts] = await Promise.all([
        handleGetIssuerSchemas({ fullHost, authorization }),
        handleGetIssuerSchemasDraft({ fullHost, authorization }),
    ]);
    return [...schemas, ...drafts];
};

export const useGetAndSetSchemasOfIssuer = ({
    fullHost,
    authorization,
}: {
    fullHost?: string;
    authorization?: string;
} = {}) => {
    const enabled = Boolean(fullHost && authorization);
    const qc = useQueryClient();

    const query = useQuery({
        queryKey: queryKey(fullHost, authorization),
        queryFn: () => fetchSchemas(fullHost as string, authorization as string),
        enabled,
    });

    const refresh = async () => {
        if (!enabled) return;
        await qc.invalidateQueries({ queryKey: queryKey(fullHost, authorization) });
        await query.refetch();
    };

    return { ...query, refresh };
};
