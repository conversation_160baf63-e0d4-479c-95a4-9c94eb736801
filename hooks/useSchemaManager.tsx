import { SchemaAttribute, SchemaVersion } from '@/types/schemaManager';
import { useEffect, useState, useMemo, useCallback, useRef } from 'react';
import { useDeploymentDetails } from './useDeploymentDetails';
import { DeploymentType } from '@/types/deployments';
import { useModal } from './useModal';
import { useTranslations } from 'next-intl';
import { ModalLogicAddSchema } from '@/components/Modals/ModalLogic.issuer.add_schema';
import { SuccessToast } from '@/components/Toasts';
import { useToast } from './useToast';
import { IssuerSchemaBuilder } from '@/types/issuerSchema';
import { convertSchemaBodyToAttributes } from '@/common/convertSchemaBodyToAttributes';
import { useGetAndSetSchemasOfIssuer } from './useGetAndSetSchemasOfIssuer';
import { generateJsonSchema } from '@/common/schemaBuilderTools';
import { SchemaBuilderStepEnum, useSchemaBuilderContext } from '@/contexts/SchemaBuilderContext';
import { SchemaStatus } from '@/const/SchemaStatus';
import { useErrorHandling } from './useErrorHandling';
import { handlePostIssuerSchemaDraft } from '@/api/issuerSchema';
import { ErrorCreateNewIssuerSchema } from '@/errors/ErrorCreateNewIssuerSchema';

const TYPE = DeploymentType.ISSUER;

export const useSchemaManager = () => {
    const { activeVersionId: schemaId, setStep, setActiveVersionId } = useSchemaBuilderContext();
    const { withErrorHandling } = useErrorHandling();

    const { data } = useDeploymentDetails({ type: TYPE });
    const { data: schemas = [], refetch } = useGetAndSetSchemasOfIssuer({
        fullHost: data?.fullHost,
        authorization: data?.authKey || undefined,
    });
    const { showModal } = useModal();
    const t = useTranslations();
    const { showToast } = useToast();

    const [schemaVersion, setSchemaVersion] = useState<SchemaVersion>({
        name: '',
        version: 1,
        type: '',
        description: '',
        attributes: [],
    });
    const [editingAttribute, setEditingAttribute] = useState<string | null>(null);
    const [isUpdatingFromJson, setIsUpdatingFromJson] = useState(false);
    const [isAutoSaving, setIsAutoSaving] = useState(false);
    const [lastAutoSaved, setLastAutoSaved] = useState<Date | null>(null);

    // Ref for debounce timeout
    const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);

    // Keep latest schemaVersion in a ref to avoid stale closures in debounced saves
    const schemaVersionRef = useRef<SchemaVersion>(schemaVersion);
    useEffect(() => {
        schemaVersionRef.current = schemaVersion;
    }, [schemaVersion]);

    // const allDraftsOfSchemaType = schemas;
    const allDraftsOfSchemaType = useMemo(() => {
        if (!schemaVersion.type || !schemaVersion.version) {
            return [];
        }

        return schemas
            .filter(
                schema =>
                    schema.type === schemaVersion.type &&
                    schema.version === schemaVersion.version &&
                    schema.status === SchemaStatus.DRAFT // Assuming drafts have status 'draft'
            )
            .sort((a, b) => {
                // Sort by updatedAt in descending order (newest first)
                return new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime();
            });
    }, [schemas, schemaVersion.type, schemaVersion.version]);

    useEffect(() => {
        if (data?.id && data.fullHost && data.authKey) {
            refetch();
        }
    }, [data, refetch]);

    // Load schema data if schemaId is provided — using fetched list
    useEffect(() => {
        if (schemaId && data?.fullHost && schemas.length > 0) {
            const existingSchema = schemas.find(s => s.id === schemaId);

            if (existingSchema) {
                const setupData: any = {
                    name: existingSchema.name,
                    version: existingSchema.version,
                    type: existingSchema.type,
                    description: existingSchema.description,
                };

                if (existingSchema.schemaBody) {
                    const attributes = convertSchemaBodyToAttributes(existingSchema.schemaBody);
                    setupData['attributes'] = attributes;
                }

                setSchemaVersion(setupData);
            }
        }
    }, [schemaId, data?.fullHost]);

    // Helper function to add nested attribute to a parent
    const addNestedAttributeRecursive = (
        attributes: SchemaAttribute[],
        parentId: string,
        newAttribute: SchemaAttribute
    ): SchemaAttribute[] => {
        return attributes.map(attr => {
            if (attr.id === parentId) {
                return {
                    ...attr,
                    properties: [...(attr.properties || []), newAttribute],
                };
            }
            if (attr.properties) {
                return {
                    ...attr,
                    properties: addNestedAttributeRecursive(attr.properties, parentId, newAttribute),
                };
            }
            return attr;
        });
    };

    const addAttribute = () => {
        debouncedAutoSave();
        const newAttribute: SchemaAttribute = {
            id: `attr_${Date.now()}`,
            name: '',
            title: '',
            dataType: 'string',
            description: '',
            required: false,
        };

        setSchemaVersion(prev => ({
            ...prev,
            attributes: [...prev.attributes, newAttribute],
        }));
        setEditingAttribute(newAttribute.id);
    };

    const addNestedAttribute = (parentId: string) => {
        const parent = findAttributeById(schemaVersion.attributes, parentId);
        if (!parent) return;
        debouncedAutoSave();

        const newAttribute: SchemaAttribute = {
            id: `attr_${Date.now()}`,
            name: `nested_attribute_${(parent.properties?.length || 0) + 1}`,
            title: `Nested Attribute ${(parent.properties?.length || 0) + 1}`,
            dataType: 'string',
            description: '',
            required: false,
        };

        setSchemaVersion(prev => ({
            ...prev,
            attributes: addNestedAttributeRecursive(prev.attributes, parentId, newAttribute),
        }));
        setEditingAttribute(newAttribute.id);
    };

    const removeAttribute = (id: string) => {
        debouncedAutoSave();
        setSchemaVersion(prev => ({
            ...prev,
            attributes: removeAttributeRecursive(prev.attributes, id),
        }));
        if (editingAttribute === id) {
            setEditingAttribute(null);
        }
    };

    // Helper function to find attribute by ID recursively
    const findAttributeById = (attributes: SchemaAttribute[], id: string): SchemaAttribute | null => {
        for (const attr of attributes) {
            if (attr.id === id) {
                return attr;
            }
            if (attr.properties) {
                const found = findAttributeById(attr.properties, id);
                if (found) return found;
            }
        }
        return null;
    };

    // Helper function to update attribute recursively
    const updateAttributeRecursive = (
        attributes: SchemaAttribute[],
        id: string,
        updates: Partial<SchemaAttribute>
    ): SchemaAttribute[] => {
        return attributes.map(attr => {
            if (attr.id === id) {
                return { ...attr, ...updates };
            }
            if (attr.properties) {
                return {
                    ...attr,
                    properties: updateAttributeRecursive(attr.properties, id, updates),
                };
            }
            return attr;
        });
    };

    // Helper function to remove attribute recursively
    const removeAttributeRecursive = (attributes: SchemaAttribute[], id: string): SchemaAttribute[] => {
        return attributes.filter(attr => {
            if (attr.id === id) {
                return false;
            }
            if (attr.properties) {
                attr.properties = removeAttributeRecursive(attr.properties, id);
            }
            return true;
        });
    };

    const updateAttribute = (id: string, updates: Partial<SchemaAttribute>) => {
        debouncedAutoSave();
        setSchemaVersion(prev => ({
            ...prev,
            attributes: updateAttributeRecursive(prev.attributes, id, updates),
        }));
    };

    // Function to update basic schema fields (name, type, description, version)
    const updateSchemaBasicFields = (
        updates: Partial<Pick<SchemaVersion, 'name' | 'type' | 'description' | 'version'>>
    ) => {
        debouncedAutoSave();
        setSchemaVersion(prev => ({
            ...prev,
            ...updates,
        }));
    };

    const handleBackToSchemaList = () => {
        setActiveVersionId(allDraftsOfSchemaType[0]?.id || null);
        setStep(SchemaBuilderStepEnum.SCHEMA_LIST);
    };

    const handlePublishSchema = () => {
        const jsonSchema = handlePreparePayload();
        if (!data) return;
        const { fullHost, authKey } = data;
        showModal(
            <ModalLogicAddSchema
                fullHost={fullHost}
                payload={jsonSchema}
                authKey={authKey || ''}
                callback={handleBackToSchemaList}
            />
        );
    };

    const handleSaveDraftSchema = (sv: SchemaVersion = schemaVersionRef.current) =>
        withErrorHandling(async () => {
            try {
                const jsonSchema = handlePreparePayload(sv);
                if (!data) return;
                const { fullHost, authKey } = data;

                await handlePostIssuerSchemaDraft({
                    fullHost: fullHost,
                    authorization: authKey || '',
                    payload: jsonSchema,
                });

                refetch();
            } catch (error) {
                throw new ErrorCreateNewIssuerSchema(error);
            }
        });

    const getFlattenedAttributes = (
        attributes: SchemaAttribute[] = schemaVersion.attributes,
        parentId: string | null = null,
        depth: number = 0
    ): Array<{
        id: string;
        attribute: SchemaAttribute;
        parentId: string | null;
        depth: number;
        index: number;
    }> => {
        const result: Array<{
            id: string;
            attribute: SchemaAttribute;
            parentId: string | null;
            depth: number;
            index: number;
        }> = [];

        attributes.forEach((attr, index) => {
            result.push({
                id: attr.id,
                attribute: attr,
                parentId,
                depth,
                index,
            });

            if (attr.properties && attr.properties.length > 0) {
                result.push(...getFlattenedAttributes(attr.properties, attr.id, depth + 1));
            }
        });

        return result;
    };

    const handlePreparePayload = (sv: SchemaVersion = schemaVersionRef.current) => {
        const jsonSchema = generateJsonSchema(sv);

        // Transform the schema to the required format
        const transformedPayload = {
            type: jsonSchema.$metadata.type,
            name: jsonSchema.title,
            description: jsonSchema.description,
            credentialSubject: jsonSchema.properties.credentialSubject,
        };

        return transformedPayload;
    };

    // Auto-save function with debounce
    const autoSaveDraft = useCallback(async () => {
        const current = schemaVersionRef.current;
        if (!current) return;

        try {
            setIsAutoSaving(true);
            // Await to ensure we save the latest state
            await handleSaveDraftSchema(current);
            setLastAutoSaved(new Date());
        } catch (error) {
            console.error('Auto-save failed:', error);
        } finally {
            setTimeout(() => {
                setIsAutoSaving(false);
            }, 3000);
        }
    }, [isUpdatingFromJson]);

    // Debounced auto-save function
    const debouncedAutoSave = useCallback(() => {
        if (debounceTimeoutRef.current) {
            clearTimeout(debounceTimeoutRef.current);
        }

        debounceTimeoutRef.current = setTimeout(() => {
            autoSaveDraft();
        }, 4000); // 2 second debounce
    }, [autoSaveDraft]);

    const handleCopyToClipboard = async () => {
        const jsonSchema = generateJsonSchema(schemaVersion);
        const content = JSON.stringify(jsonSchema, null, 2);
        navigator.clipboard.writeText(content);
        showToast(<SuccessToast title={t('toast.success')} message={t('toast.copied_to_clipboard')} />);
    };

    // Function to convert JSON schema properties to SchemaAttribute[]
    const convertPropertiesToAttributes = (
        properties: Record<string, any>,
        requiredFields: string[] = []
    ): SchemaAttribute[] => {
        return Object.entries(properties).map(([key, value], index) => {
            const attribute: SchemaAttribute = {
                id: `attr_${Date.now()}_${index}_${Math.random().toString(36).substr(2, 9)}`,
                name: key,
                title: value.title || key,
                dataType: value.type || 'string',
                description: value.description || '',
                required: requiredFields.includes(key),
            };

            // Handle nested properties for object types
            if (value.type === 'object' && value.properties) {
                const nestedRequired = value.required || [];
                attribute.properties = convertPropertiesToAttributes(value.properties, nestedRequired);
            }

            return attribute;
        });
    };

    // Function to import schema from JSON
    const importSchemaFromJson = (jsonString: string): { success: boolean; error?: string } => {
        try {
            setIsUpdatingFromJson(true);
            const parsedSchema: IssuerSchemaBuilder = JSON.parse(jsonString);

            // Validate basic structure
            if (!parsedSchema.$metadata || !parsedSchema.title || !parsedSchema.properties?.credentialSubject) {
                setIsUpdatingFromJson(false);
                return { success: false, error: 'Invalid schema structure. Missing required fields.' };
            }

            // Convert properties to attributes
            const credentialSubject = parsedSchema.properties.credentialSubject;
            const requiredFields = credentialSubject.required || [];
            const attributes = convertPropertiesToAttributes(credentialSubject.properties || {}, requiredFields);

            // Update schema version
            setSchemaVersion({
                name: parsedSchema.title,
                version: parsedSchema.$metadata.version,
                type: parsedSchema.$metadata.type,
                description: parsedSchema.description || '',
                attributes: attributes,
            });

            // Reset editing state
            setEditingAttribute(null);

            return { success: true };
        } catch (error) {
            setIsUpdatingFromJson(false);
            const errorMessage = error instanceof Error ? error.message : 'Invalid JSON format';
            return { success: false, error: errorMessage };
        }
    };

    // Cleanup timeout on unmount
    useEffect(() => {
        return () => {
            if (debounceTimeoutRef.current) {
                clearTimeout(debounceTimeoutRef.current);
            }
        };
    }, []);

    return {
        schemaVersion,
        editingAttribute,
        setEditingAttribute,
        addAttribute,
        addNestedAttribute,
        removeAttribute,
        generateJsonSchema,
        updateAttribute,
        updateSchemaBasicFields,
        findAttributeById,
        handlePublishSchema,
        handleSaveDraftSchema,
        handleCopyToClipboard,
        handlePreparePayload,
        importSchemaFromJson,
        handleBackToSchemaList,
        isUpdatingFromJson,
        allDraftsOfSchemaType,
        isAutoSaving,
        lastAutoSaved,
    };
};
