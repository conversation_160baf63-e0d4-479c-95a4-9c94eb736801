import { DeploymentsTypesEnum } from '@/common/deploymentsTypes';
import { BlockchainNetworkName } from '@/types/blockchanConfigs';

export enum DeploymentStatus {
    INIT = 'INIT',
    PENDING = 'PENDING',
    FAILED = 'FAILED',
    ACTIVE = 'ACTIVE',
    DELETED_SCHEDULED = 'DELETED_SCHEDULED',
    UPGRADE = 'UPGRADE',
    FAILED_DELETION = 'FAILED_DELETION',
    READY_TO_PUT_TO_BLOCKCHAIN = 'READY_TO_PUT_TO_BLOCKCHAIN',
    TOKEN_ON_ISSUER = 'TOKEN_ON_ISSUER',
}

export enum DeploymentType {
    ISSUER = 'issuer',
    VERIFIER = 'verifier',
}

export const DeploymentStatusColors = {
    [DeploymentStatus.INIT]: 'bg-status-init',
    [DeploymentStatus.PENDING]: 'bg-status-pending',
    [DeploymentStatus.FAILED]: 'bg-status-failed',
    [DeploymentStatus.ACTIVE]: 'bg-status-active',
    [DeploymentStatus.DELETED_SCHEDULED]: 'bg-status-deleted',
    [DeploymentStatus.UPGRADE]: 'bg-status-pending',
    [DeploymentStatus.FAILED_DELETION]: 'bg-status-pending',
    [DeploymentStatus.READY_TO_PUT_TO_BLOCKCHAIN]: 'bg-status-pending',
    [DeploymentStatus.TOKEN_ON_ISSUER]: 'bg-status-pending',
};

export const DeploymentStatusIcons = {
    [DeploymentStatus.INIT]: '/icons/check_pending.svg',
    [DeploymentStatus.PENDING]: '/icons/check_pending.svg',
    [DeploymentStatus.FAILED]: '/icons/check_cancel.svg',
    [DeploymentStatus.ACTIVE]: '/icons/check_circle.svg',
    [DeploymentStatus.DELETED_SCHEDULED]: '/icons/check_cancel.svg',
    [DeploymentStatus.UPGRADE]: '/icons/check_info.svg',
    [DeploymentStatus.FAILED_DELETION]: '/icons/check_cancel.svg',
    [DeploymentStatus.READY_TO_PUT_TO_BLOCKCHAIN]: '/icons/check_pending.svg',
    [DeploymentStatus.TOKEN_ON_ISSUER]: '/icons/check_pending.svg',
};

export interface deploymentsParams {
    type: DeploymentType;
}

export interface getDeploymentsParams extends deploymentsParams {
    id: number;
}

export interface createDeploymentParams extends deploymentsParams {
    name: string;
}

export type DeploymentCreateRequest = {
    deploymentType: DeploymentsTypesEnum;
} & (
    | {
          issuerName: string;
      }
    | {
          verifierName: string;
      }
);

export type Deployment = {
    id: string;
    fullHost: string;
    status: DeploymentStatus;
    userId: number;
    deploymentType: DeploymentsTypesEnum;
    networkName: BlockchainNetworkName;
    version: string;
    didDocument?: string;
} & (
    | {
          issuerName: string;
          authKey: string;
          issuerReadableName: string;
          verifierReadableName?: never;
          verifierName?: never;
          issuerCreatorAddress: string;
          verifierCreatorAddress?: never;
          organization: string;
      }
    | {
          issuerName?: never;
          authKey?: never;
          issuerReadableName?: never;
          verifierReadableName: string;
          verifierName: string;
          issuerCreatorAddress?: never;
          verifierCreatorAddress: string;
          organization?: never;
          didDocument?: never;
      }
);

export type UpgradeDeploymentSecretResponse = {
    newClientSecret: string;
};
